using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using OpenIddict.Abstractions;
using OpenIddict.Server.AspNetCore;
using ShiningCMusicApi.Services.Interfaces;
using System.Security.Claims;
using static OpenIddict.Abstractions.OpenIddictConstants;

namespace ShiningCMusicApi.Controllers;

[ApiController]
[Route("connect")]
public class AuthorizationController : ControllerBase
{
    private readonly IOpenIddictApplicationManager _applicationManager;
    private readonly IClientSecretService _clientSecretService;
    private readonly ILogger<AuthorizationController> _logger;

    public AuthorizationController(
        IOpenIddictApplicationManager applicationManager,
        IClientSecretService clientSecretService,
        ILogger<AuthorizationController> logger)
    {
        _applicationManager = applicationManager;
        _clientSecretService = clientSecretService;
        _logger = logger;
    }

    [HttpPost("token")]
    [AllowAnonymous]
    [Produces("application/json")]
    public async Task<IActionResult> Exchange()
    {
        _logger.LogInformation("Token endpoint called");
        
        var request = HttpContext.GetOpenIddictServerRequest();
        if (request == null)
        {
            _logger.LogError("OpenIddict request is null");
            return BadRequest(new OpenIddictResponse
            {
                Error = Errors.InvalidRequest,
                ErrorDescription = "The request is malformed."
            });
        }

        _logger.LogInformation("Grant type: {GrantType}, Client ID: {ClientId}", request.GrantType, request.ClientId);

        if (request.IsClientCredentialsGrantType())
        {
            // Note: the client credentials are automatically validated by OpenIddict:
            // if client_id or client_secret are invalid, this action won't be invoked.

            var application = await _applicationManager.FindByClientIdAsync(request.ClientId ?? string.Empty);
            if (application == null)
            {
                _logger.LogError("Application not found for client_id: {ClientId}", request.ClientId);
                return BadRequest(new OpenIddictResponse
                {
                    Error = Errors.InvalidClient,
                    ErrorDescription = "The specified client identifier is invalid."
                });
            }

            // Create a new ClaimsIdentity containing the claims that will be used to create tokens
            var identity = new ClaimsIdentity(
                authenticationType: TokenValidationParameters.DefaultAuthenticationType,
                nameType: Claims.Name,
                roleType: Claims.Role);

            // Use the client_id as the subject identifier
            identity.SetClaim(Claims.Subject, request.ClientId ?? throw new InvalidOperationException());
            identity.SetClaim(Claims.Name, request.ClientId ?? throw new InvalidOperationException());

            identity.SetScopes(request.GetScopes());
            identity.SetResources(new[] { "ShiningCMusicApi" });
            identity.SetDestinations(GetDestinations);

            var principal = new ClaimsPrincipal(identity);

            // Get custom token lifetime from database
            var clientId = request.ClientId;
            if (!string.IsNullOrEmpty(clientId))
            {
                try
                {
                    var tokenLifetimeSeconds = await _clientSecretService.GetTokenLifetimeAsync(clientId);
                    principal.SetAccessTokenLifetime(TimeSpan.FromSeconds(tokenLifetimeSeconds));
                    _logger.LogInformation("Set custom token lifetime: {TokenLifetime} seconds for client: {ClientId}", 
                        tokenLifetimeSeconds, clientId);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to get custom token lifetime for client: {ClientId}, using default", clientId);
                }
            }

            _logger.LogInformation("Returning token for client: {ClientId}", request.ClientId);
            return SignIn(principal, OpenIddictServerAspNetCoreDefaults.AuthenticationScheme);
        }

        _logger.LogError("Unsupported grant type: {GrantType}", request.GrantType);
        return BadRequest(new OpenIddictResponse
        {
            Error = Errors.UnsupportedGrantType,
            ErrorDescription = "The specified grant type is not supported."
        });
    }



    private static IEnumerable<string> GetDestinations(Claim claim)
    {
        switch (claim.Type)
        {
            case Claims.Name:
                yield return Destinations.AccessToken;
                yield break;

            case Claims.Email:
                yield return Destinations.AccessToken;
                yield break;

            case Claims.Role:
                yield return Destinations.AccessToken;
                yield break;

            default:
                yield return Destinations.AccessToken;
                yield break;
        }
    }
}
