using Microsoft.AspNetCore.Mvc;
using OpenIddict.Abstractions;

namespace ShiningCMusicApi.Controllers;

[ApiController]
[Route("api/[controller]")]
public class TestController : ControllerBase
{
    private readonly IOpenIddictApplicationManager _applicationManager;
    private readonly ILogger<TestController> _logger;

    public TestController(
        IOpenIddictApplicationManager applicationManager,
        ILogger<TestController> logger)
    {
        _applicationManager = applicationManager;
        _logger = logger;
    }

    [HttpGet("openiddict-status")]
    public async Task<IActionResult> GetOpenIddictStatus()
    {
        try
        {
            _logger.LogInformation("Testing OpenIddict status");

            // Test if we can access the application manager
            var clientCount = 0;
            await foreach (var app in _applicationManager.ListAsync())
            {
                clientCount++;
                if (clientCount > 10) break; // Prevent infinite loop
            }

            return Ok(new
            {
                message = "OpenIddict is working",
                clientCount = clientCount,
                timestamp = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error testing OpenIddict status");
            return StatusCode(500, new
            {
                message = "OpenIddict error",
                error = ex.Message,
                timestamp = DateTime.UtcNow
            });
        }
    }
}
