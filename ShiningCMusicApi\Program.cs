using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Authentication;
using OpenIddict.Validation.AspNetCore;
using System.Security.Claims;
using ShiningCMusicApi.Infrastructure;
using ShiningCMusicApi.Services;
using ShiningCMusicApi.Services.Interfaces;
using ShiningCMusicApi.Services.Implementations;
using ShiningCMusicApi.Configuration;

var builder = WebApplication.CreateBuilder(args);

// Add logging
builder.Services.AddLogging(logging =>
{
    logging.AddConsole();
    logging.AddDebug();
});

// Add services to the container.

// Add connection string
builder.Services.AddSingleton<IConfiguration>(builder.Configuration);

// Add our services
builder.Services.AddScoped<ILessonService, LessonService>();
builder.Services.AddScoped<ITutorService, TutorService>();
builder.Services.AddScoped<IStudentService, StudentService>();
builder.Services.AddScoped<ISubjectService, SubjectService>();
builder.Services.AddScoped<ILocationService, LocationService>();
builder.Services.AddScoped<IUserService, UserService>();
builder.Services.AddScoped<IPasswordService, PasswordService>();
builder.Services.AddScoped<IEmailTemplateService, EmailTemplateService>();
builder.Services.AddScoped<IEmailService, EmailService>();
builder.Services.AddScoped<ITimesheetService, TimesheetService>();
builder.Services.AddScoped<IConfigService, ConfigService>();

// Add background services
builder.Services.AddHostedService<ShiningCMusicApi.Services.BackgroundServices.LessonCleanupService>();
builder.Services.AddHostedService<ShiningCMusicApi.Services.BackgroundServices.TutorCleanupService>();
builder.Services.AddHostedService<ShiningCMusicApi.Services.BackgroundServices.StudentCleanupService>();
builder.Services.AddHostedService<ShiningCMusicApi.Services.BackgroundServices.TimesheetCleanupService>();
builder.Services.AddHostedService<ShiningCMusicApi.Services.BackgroundServices.PaymentReminderService>();

// Add OpenIddict seeder
builder.Services.AddHostedService<OpenIddictSeeder>();

// Add ApplicationDbContext for OpenIddict (includes plain text secrets table)
builder.Services.AddDbContext<ApplicationDbContext>(options =>
{
    options.UseSqlServer(Environment.GetEnvironmentVariable("DATABASE_CONNECTION_STRING") ?? builder.Configuration.GetConnectionString("MusicSchool"));
    options.UseOpenIddict();
});

// Register services
builder.Services.AddScoped<IClientSecretService, ClientSecretService>();
builder.Services.AddScoped<CustomTokenLifetimeHandler>();

// AuthConfig removed - no longer needed without IdentityServer4

// Configure OpenIddict
builder.Services.AddOpenIddict()
    .AddCore(options =>
    {
        options.UseEntityFrameworkCore()
               .UseDbContext<ApplicationDbContext>();
    })
    .AddServer(options =>
    {
        // Enable the token and discovery endpoints
        options.SetTokenEndpointUris("/connect/token");
        options.SetConfigurationEndpointUris("/.well-known/openid_configuration");

        // Configure token lifetimes (ADD THIS)
        options.SetAccessTokenLifetime(TimeSpan.FromHours(2)); // 2 hours

        // Enable the client credentials flow
        options.AllowClientCredentialsFlow();

        // Register the signing and encryption credentials
        if (builder.Environment.IsDevelopment())
        {
            options.AddDevelopmentEncryptionCertificate();
            options.AddDevelopmentSigningCertificate();
        }
        else
        {
            // Use real certificates, or use ephemeral keys
            options.AddEphemeralEncryptionKey();
            options.AddEphemeralSigningKey();
        }

        // Register the ASP.NET Core host and configure the ASP.NET Core options
        options.UseAspNetCore()
               .DisableTransportSecurityRequirement();

        // Add custom event handler for token requests
        options.AddEventHandler<OpenIddict.Server.OpenIddictServerEvents.HandleTokenRequestContext>(builder =>
            builder.UseScopedHandler<CustomTokenLifetimeHandler>());
    })
    .AddValidation(options =>
    {
        // Import the configuration from the local OpenIddict server instance
        options.UseLocalServer();

        // Register the ASP.NET Core host
        options.UseAspNetCore();
    });

// IdentityServer4 removed - using OpenIddict only

var apiBaseUrl = Environment.GetEnvironmentVariable("API_BASE_URL") ?? builder.Configuration["ApiBaseUrl"];

// Add Authentication - OpenIddict only
builder.Services.AddAuthentication(options =>
{
    options.DefaultAuthenticateScheme = OpenIddictValidationAspNetCoreDefaults.AuthenticationScheme;
    options.DefaultChallengeScheme = OpenIddictValidationAspNetCoreDefaults.AuthenticationScheme;
});

// Add CORS for Blazor client
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowBlazorApp", policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyHeader()
              .AllowAnyMethod();
    });
});

// Add Authorization policies
builder.Services.AddAuthorization(options =>
{
    options.AddPolicy("AdminOnly", policy => policy.RequireRole("Administrator"));
    options.AddPolicy("TutorOrAdmin", policy => policy.RequireRole("Tutor", "Administrator"));
    options.AddPolicy("StudentOrAdmin", policy => policy.RequireRole("Student", "Administrator"));
    options.AddPolicy("AuthenticatedUser", policy => policy.RequireAuthenticatedUser());
});

builder.Services.AddControllers();
// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();
builder.Services.AddMemoryCache();

var app = builder.Build();

// Add diagnostic logging for startup
var logger = app.Services.GetRequiredService<ILogger<Program>>();
logger.LogInformation("Application starting up...");

try
{
    // Test OpenIddict services are registered
    var applicationManager = app.Services.GetRequiredService<OpenIddict.Abstractions.IOpenIddictApplicationManager>();
    logger.LogInformation("OpenIddict ApplicationManager successfully resolved");
}
catch (Exception ex)
{
    logger.LogError(ex, "Failed to resolve OpenIddict ApplicationManager");
}

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseHttpsRedirection();

app.UseRouting();
app.UseCors("AllowBlazorApp");

app.UseAuthentication();
app.UseAuthorization();

app.UseEndpoints(endpoints =>
{
    endpoints.MapControllers();
    endpoints.MapDefaultControllerRoute();
});

app.Run();
