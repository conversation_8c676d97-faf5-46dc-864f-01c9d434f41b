<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <system.webServer>
    <!-- URL Rewrite rules for BFF and API routing -->
    <rewrite>
      <rules>
        <!-- BFF API routes - forward to BFF subdirectory -->
        <rule name="BFF Routes" stopProcessing="true">
          <match url="^bff/(.*)$" />
          <action type="Rewrite" url="bff/{R:1}" />
        </rule>
        
        <!-- API routes - handle normally -->
        <rule name="API Routes" stopProcessing="true">
          <match url="^api/(.*)$" />
          <action type="Rewrite" url="api/{R:1}" />
        </rule>
        
        <!-- Blazor fallback for SPA routing -->
        <rule name="Blazor Routes" stopProcessing="true">
          <match url=".*" />
          <conditions logicalGrouping="MatchAll">
            <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true" />
            <add input="{REQUEST_FILENAME}" matchType="IsDirectory" negate="true" />
            <add input="{REQUEST_URI}" pattern="^/(api|bff|connect|\.well-known)" negate="true" />
          </conditions>
          <action type="Rewrite" url="/" />
        </rule>
      </rules>
    </rewrite>
    
    <!-- Enable compression for better performance -->
    <urlCompression doStaticCompression="true" doDynamicCompression="true" />
    
    <!-- Set proper MIME types for Blazor WebAssembly -->
    <staticContent>
      <mimeMap fileExtension=".wasm" mimeType="application/wasm" />
      <mimeMap fileExtension=".dll" mimeType="application/octet-stream" />
      <mimeMap fileExtension=".dat" mimeType="application/octet-stream" />
      <mimeMap fileExtension=".blat" mimeType="application/octet-stream" />
      <mimeMap fileExtension=".br" mimeType="application/octet-stream" />
    </staticContent>
    
    <!-- Security headers -->
    <httpProtocol>
      <customHeaders>
        <add name="X-Content-Type-Options" value="nosniff" />
        <add name="X-Frame-Options" value="DENY" />
        <add name="X-XSS-Protection" value="1; mode=block" />
      </customHeaders>
    </httpProtocol>
  </system.webServer>
</configuration>
