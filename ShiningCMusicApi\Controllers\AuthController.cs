using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using OpenIddict.Abstractions;
using OpenIddict.Server.AspNetCore;
using System.Security.Claims;
using static OpenIddict.Abstractions.OpenIddictConstants;

namespace ShiningCMusicApi.Controllers;

[ApiController]
[Route("connect")]
public class AuthController : ControllerBase
{
    private readonly IOpenIddictApplicationManager _applicationManager;
    private readonly ILogger<AuthController> _logger;

    public AuthController(
        IOpenIddictApplicationManager applicationManager,
        ILogger<AuthController> logger)
    {
        _applicationManager = applicationManager;
        _logger = logger;
    }

    [HttpPost("token")]
    [AllowAnonymous]
    [Produces("application/json")]
    public async Task<IActionResult> Exchange()
    {
        _logger.LogInformation("Token endpoint called");
        
        var request = HttpContext.GetOpenIddictServerRequest();
        if (request == null)
        {
            _logger.LogError("OpenIddict request is null");
            return BadRequest("Invalid request");
        }

        _logger.LogInformation("Grant type: {GrantType}", request.GrantType);

        if (request.IsClientCredentialsGrantType())
        {
            // Note: the client credentials are automatically validated by OpenIddict:
            // if client_id or client_secret are invalid, this action won't be invoked.

            var application = await _applicationManager.FindByClientIdAsync(request.ClientId ?? string.Empty);
            if (application == null)
            {
                _logger.LogError("Application not found for client_id: {ClientId}", request.ClientId);
                return BadRequest("Invalid client");
            }

            // Create a new ClaimsIdentity containing the claims that will be used to create tokens
            var identity = new ClaimsIdentity(
                authenticationType: TokenValidationParameters.DefaultAuthenticationType,
                nameType: Claims.Name,
                roleType: Claims.Role);

            // Use the client_id as the subject identifier
            identity.SetClaim(Claims.Subject, request.ClientId ?? throw new InvalidOperationException());
            identity.SetClaim(Claims.Name, request.ClientId ?? throw new InvalidOperationException());

            identity.SetScopes(request.GetScopes());
            identity.SetResources(new[] { "ShiningCMusicApi" });
            identity.SetDestinations(GetDestinations);

            var principal = new ClaimsPrincipal(identity);

            _logger.LogInformation("Returning token for client: {ClientId}", request.ClientId);
            return SignIn(principal, OpenIddictServerAspNetCoreDefaults.AuthenticationScheme);
        }

        _logger.LogError("Unsupported grant type: {GrantType}", request.GrantType);
        return BadRequest("Unsupported grant type");
    }

    private static IEnumerable<string> GetDestinations(Claim claim)
    {
        switch (claim.Type)
        {
            case Claims.Name:
                yield return Destinations.AccessToken;
                yield break;

            case Claims.Email:
                yield return Destinations.AccessToken;
                yield break;

            case Claims.Role:
                yield return Destinations.AccessToken;
                yield break;

            default:
                yield return Destinations.AccessToken;
                yield break;
        }
    }
}
